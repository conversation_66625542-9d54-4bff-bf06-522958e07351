<template>
  <div class="dashboard-container">
    <div class="dashboard-content">
      <div class="dashboard-header">
        <!-- <h1 class="dashboard-title">数字人</h1> -->
        <div class="status-indicator">
          <span class="status-dot" :class="{
            'connected': connectionStatus==='connected',
            'connecting': connectionStatus==='connecting',
            'disconnected': connectionStatus==='disconnected'
          }"></span>
          <span class="status-text">{{ statusText }}</span>
        </div>
      </div>

      <!-- 数字人视频容器 - 居中显示 -->
      <div class="video-container">
        <div class="video-wrapper">
          <div class="video-frame">
            <div class="video-content" :style="{ height: videoHeight }">
              <video ref="videoEl" autoplay playsinline class="video-element"></video>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部工具栏 - 固定在底部，不覆盖视频 -->
      <div class="toolbar-container">
        <div class="toolbar">
          <div class="toolbar-content">
            <!-- 左侧：连接/录制 -->
            <div class="toolbar-left">
              <button v-if="!connected" @click="onStart" class="btn btn-primary">
                <i class="bi bi-play-fill"></i> 开始连接
              </button>
              <button v-else @click="onStop" class="btn btn-danger">
                <i class="bi bi-stop-fill"></i> 停止连接
              </button>

              <button @click="startRecord" :disabled="!connected || recording" class="btn btn-secondary">
                <i class="bi bi-record-fill"></i> 开始录制
              </button>
              <button @click="stopRecord" :disabled="!connected || !recording" class="btn btn-secondary">
                <i class="bi bi-stop-fill"></i> 停止录制
              </button>
            </div>

            <!-- 中间：按住说话 -->
            <div class="toolbar-center">
              <div
                @mousedown.prevent="pressToTalk(true)"
                @mouseup.prevent="pressToTalk(false)"
                @mouseleave.prevent="pressToTalk(false)"
                @touchstart.prevent="pressToTalk(true)"
                @touchend.prevent="pressToTalk(false)"
                class="mic-button"
                :class="recording ? 'recording' : 'idle'">
                <i class="bi bi-mic-fill"></i>
              </div>
            </div>

            <!-- 右侧：功能入口 -->
            <div class="toolbar-right">
              <button @click="showChat = true" class="btn btn-secondary">
                <i class="bi bi-chat-dots"></i> 交互
              </button>
              <button @click="showSettings = true" class="btn btn-secondary">
                <i class="bi bi-sliders"></i> 设置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧抽屉：交互（对话 / 朗读） -->
      <div v-if="showChat" class="sidebar-overlay">
        <div class="sidebar-backdrop" @click="showChat=false"></div>
        <div class="sidebar">
          <div class="sidebar-header">
            <div class="sidebar-title">互动面板</div>
            <button @click="showChat=false" class="btn btn-secondary"><i class="bi bi-x-lg"></i></button>
          </div>
          <div class="sidebar-content">
            <div class="tab-container">
              <button :class="tab==='chat' ? 'tab-button active' : 'tab-button'" @click="tab='chat'">对话模式</button>
              <button :class="tab==='tts' ? 'tab-button active' : 'tab-button'" @click="tab='tts'">朗读模式</button>
            </div>

            <!-- 对话模式 -->
            <div v-show="tab==='chat'" class="chat-container">
              <div ref="chatBox" class="chat-box">
                <div class="chat-message system">系统: 欢迎使用livetalking，请点击"开始连接"按钮开始对话。</div>
                <div v-for="(m,i) in messages" :key="i" :class="'chat-message ' + (m.role==='user' ? 'user' : 'assistant')">
                  {{ m.role==='user' ? '您' : '数字人' }}: {{ m.text }}
                </div>
              </div>
              <div class="chat-input-container">
                <textarea v-model="chatInput" rows="3" placeholder="输入您想对数字人说的话..." class="chat-textarea"></textarea>
                <button @click="sendChat" class="btn btn-primary">发送</button>
              </div>
              <div class="chat-hint">按住底部麦克风可语音输入</div>
            </div>

            <!-- 朗读模式 -->
            <div v-show="tab==='tts'" class="tts-container">
              <label class="tts-label">输入要朗读的文本</label>
              <textarea v-model="ttsInput" rows="8" placeholder="输入您想让数字人朗读的文字..." class="tts-textarea"></textarea>
              <button @click="sendTTS" class="btn btn-primary" style="width: 100%;"><i class="bi bi-volume-up"></i> 朗读文本</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧抽屉：设置 -->
      <div v-if="showSettings" class="sidebar-overlay">
        <div class="sidebar-backdrop" @click="showSettings=false"></div>
        <div class="sidebar">
          <div class="sidebar-header">
            <div class="sidebar-title">设置</div>
            <button @click="showSettings=false" class="btn btn-secondary"><i class="bi bi-x-lg"></i></button>
          </div>
          <div class="sidebar-content settings-content">
            <div class="setting-item">
              <label class="setting-label">视频大小调节: <span>{{ videoScale }}%</span></label>
              <input type="range" min="50" max="150" v-model.number="videoScale" class="range-input" />
            </div>

            <!-- Avatar切换设置 -->
            <div class="setting-item">
              <label class="setting-label">Avatar形象切换</label>
              <div class="avatar-selector">
                <button @click="loadAvatarList" class="btn btn-secondary" :disabled="loadingAvatars">
                  <i class="bi bi-arrow-clockwise" :class="{ 'spin': loadingAvatars }"></i>
                  {{ loadingAvatars ? '加载中...' : '刷新列表' }}
                </button>
                <div v-if="avatarList.length > 0" class="avatar-grid">
                  <div
                    v-for="avatar in avatarList"
                    :key="avatar.avatar_id"
                    @click="!switchingAvatar && switchAvatar(avatar.avatar_id)"
                    class="avatar-card"
                    :class="{
                      'active': currentAvatarId === avatar.avatar_id,
                      'switching': switchingAvatar,
                      'disabled': switchingAvatar
                    }"
                  >
                    <div class="avatar-name">{{ avatar.name }}</div>
                    <div class="avatar-id">{{ avatar.avatar_id }}</div>
                    <div v-if="currentAvatarId === avatar.avatar_id" class="avatar-current">当前</div>
                  </div>
                </div>
                <div v-else-if="!loadingAvatars" class="avatar-empty">
                  暂无可用Avatar，请点击刷新列表
                </div>
              </div>
            </div>

            <label class="checkbox-label">
              <input type="checkbox" v-model="useStun" class="checkbox-input" />
              <span>使用STUN服务器</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="footer">中通服创立数智城市研发部</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      tab: 'chat',
      connected: false,
      recording: false,
      connectionStatus: 'disconnected',
      chatInput: '',
      ttsInput: '',
      messages: [],
      sessionId: 0,
      useStun: false,
      videoScale: 100,
      pc: null,
      mediaRecorder: null,
      audioChunks: [],
      audioStream: null,
      showChat: false,
      showSettings: false,
      // Avatar切换相关
      avatarList: [],
      currentAvatarId: null,
      loadingAvatars: false,
      switchingAvatar: false
    }
  },
  computed: {
    statusText() {
      const map = { disconnected: '未连接', connecting: '连接中...', connected: '已连接' }
      return map[this.connectionStatus] || '未连接'
    },
    videoHeight() {
      // 以 480px 为基准高度，按比例缩放
      const base = 480
      return `${Math.round(base * (this.videoScale / 100))}px`
    },

  },
  async mounted() {
    // 初始化音频录制功能
    await this.initAudioRecording()
    // 加载Avatar列表
    await this.loadAvatarList()
  },
  beforeUnmount() {
    // 清理音频资源
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop())
    }
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop()
    }
  },
  methods: {
    async onStart() {
      try {
        this.connectionStatus = 'connecting'
        const config = { sdpSemantics: 'unified-plan' }
        if (this.useStun) config.iceServers = [{ urls: ['stun:stun.l.google.com:19302'] }]

        this.pc = new RTCPeerConnection(config)
        this.pc.addEventListener('track', (evt) => {
          if (evt.track.kind === 'video') this.$refs.videoEl.srcObject = evt.streams[0]
        })
        this.pc.addTransceiver('video', { direction: 'recvonly' })
        this.pc.addTransceiver('audio', { direction: 'recvonly' })

        const offer = await this.pc.createOffer()
        await this.pc.setLocalDescription(offer)
        await new Promise((resolve) => {
          if (this.pc.iceGatheringState === 'complete') return resolve()
          const check = () => {
            if (this.pc.iceGatheringState === 'complete') {
              this.pc.removeEventListener('icegatheringstatechange', check)
              resolve()
            }
          }
          this.pc.addEventListener('icegatheringstatechange', check)
        })

        const resp = await fetch('/offer', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sdp: this.pc.localDescription.sdp, type: this.pc.localDescription.type })
        })
        if (!resp.ok) throw new Error('服务器响应错误')
        const answer = await resp.json()
        this.sessionId = answer.sessionid
        await this.pc.setRemoteDescription(answer)

        this.connected = true
        this.connectionStatus = 'connected'
      } catch (e) {
        console.error(e)
        this.connectionStatus = 'disconnected'
        this.connected = false
      }
    },
    onStop() {
      if (this.pc) {
        setTimeout(() => this.pc.close(), 500)
      }
      this.connected = false
      this.connectionStatus = 'disconnected'
    },
    async startRecord() {
      try {
        const r = await fetch('/record', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'start_record', sessionid: this.sessionId })
        })
        if (r.ok) this.recording = true
      } catch (e) { console.error(e) }
    },
    async stopRecord() {
      try {
        const r = await fetch('/record', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'end_record', sessionid: this.sessionId })
        })
        if (r.ok) this.recording = false
      } catch (e) { console.error(e) }
    },
    async sendChat() {
      const text = (this.chatInput || '').trim()
      if (!text) return
      this.messages.push({ role: 'user', text })
      this.$nextTick(() => { const el = this.$refs.chatBox; if (el) el.scrollTop = el.scrollHeight })

      try {
        await fetch('/human', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text, type: 'chat', interrupt: true, sessionid: this.sessionId })
        })
      } catch (e) { console.error(e) }
      this.chatInput = ''
    },
    async sendTTS() {
      const text = (this.ttsInput || '').trim()
      if (!text) return
      try {
        await fetch('/human', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text, type: 'echo', interrupt: true, sessionid: this.sessionId })
        })
        this.messages.push({ role: 'system', text: `已发送朗读请求: "${text}"` })
        this.$nextTick(() => { const el = this.$refs.chatBox; if (el) el.scrollTop = el.scrollHeight })
      } catch (e) { console.error(e) }
      this.ttsInput = ''
    },
    // 初始化音频录制
    async initAudioRecording() {
      try {
        this.audioStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true
          }
        })
        console.log('音频录制初始化成功')
      } catch (error) {
        console.error('无法访问麦克风:', error)
        alert('无法访问麦克风，请检查浏览器权限设置')
      }
    },

    // 按住说话功能
    async pressToTalk(start) {
      if (!this.audioStream) {
        await this.initAudioRecording()
        if (!this.audioStream) return
      }

      if (start && !this.recording) {
        this.recording = true
        this.audioChunks = []

        // 创建 MediaRecorder
        this.mediaRecorder = new MediaRecorder(this.audioStream, {
          mimeType: 'audio/webm;codecs=opus'
        })

        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.audioChunks.push(event.data)
          }
        }

        this.mediaRecorder.onstop = async () => {
          await this.processAudioRecording()
        }

        this.mediaRecorder.start()
        console.log('开始录音')

      } else if (!start && this.recording) {
        this.recording = false
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          this.mediaRecorder.stop()
          console.log('停止录音')
        }
      }
    },

    // 处理录音数据
    async processAudioRecording() {
      if (this.audioChunks.length === 0) return

      try {
        // 创建音频 Blob
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' })

        // 转换为 base64
        const base64Audio = await this.convertToBase64(audioBlob)

        // 调用语音识别接口
        const recognizedText = await this.callSpeechRecognitionAPI(base64Audio)

        if (recognizedText && recognizedText.trim()) {
          this.chatInput = recognizedText
          // 自动发送识别的文本
          this.messages.push({ role: 'user', text: recognizedText })

          // 发送到数字人接口
          fetch('/human', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ text: recognizedText, type: 'chat', interrupt: true, sessionid: this.sessionId })
          })
          this.chatInput = ''
        }

      } catch (error) {
        console.error('处理录音失败:', error)
        alert('语音识别失败，请重试')
      }
    },

    // 转换音频为 base64
    async convertToBase64(audioBlob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
          // 移除 data:audio/webm;base64, 前缀
          const base64 = reader.result.split(',')[1]
          resolve(base64)
        }
        reader.onerror = reject
        reader.readAsDataURL(audioBlob)
      })
    },

    // 调用语音识别 API
    async callSpeechRecognitionAPI(base64Audio) {
      try {
        const response = await fetch('http://10.206.23.56:8082/langchain/audio/schasrbase64', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            audio_data: base64Audio,
            format: 'webm'
          })
        })

        const result = await response.json()
        console.log(result)
        if (result.code === 200) {
          console.log('语音识别成功:', result.data)
          return result.data
        } else {
          console.error('语音识别失败:', result.msg)
          return null
        }
      } catch (error) {
        console.error('调用语音识别接口失败:', error)
        throw error
      }
    },

    // Avatar切换相关方法
    async loadAvatarList() {
      this.loadingAvatars = true
      try {
        console.log('开始请求Avatar列表...')
        // 尝试多个可能的API路径，包括完整URL作为备选
        const apiUrls = [
          '/api/avatar_list',
          '/avatar_list',
          'http://localhost:8010/avatar_list',
          'http://localhost:8010/api/avatar_list'
        ]
        let response = null
        let lastError = null

        for (const url of apiUrls) {
          try {
            console.log(`尝试请求: ${url}`)
            response = await fetch(url, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            })

            if (response.ok) {
              console.log(`成功连接到: ${url}`)
              break
            } else {
              console.log(`${url} 返回状态: ${response.status}`)
            }
          } catch (error) {
            console.log(`${url} 请求失败:`, error)
            lastError = error
          }
        }

        if (!response || !response.ok) {
          throw lastError || new Error('所有API路径都无法访问')
        }

        console.log('响应状态:', response.status)
        console.log('响应头:', Object.fromEntries(response.headers.entries()))

        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
        }

        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          const text = await response.text()
          console.error('响应不是JSON格式:', text.substring(0, 200))
          throw new Error(`服务器返回了非JSON响应: ${contentType}`)
        }

        const data = await response.json()
        console.log('解析的JSON数据:', data)

        if (data.code === 0) {
          this.avatarList = data.data
          console.log('成功加载Avatar列表:', this.avatarList)

          // 如果当前没有选中的avatar，默认选择第一个
          if (!this.currentAvatarId && this.avatarList.length > 0) {
            this.currentAvatarId = this.avatarList[0].avatar_id
          }
        } else {
          console.error('加载Avatar列表失败:', data.msg)
          alert(`加载Avatar列表失败: ${data.msg}`)
        }
      } catch (error) {
        console.error('请求Avatar列表失败:', error)
        alert(`请求Avatar列表失败: ${error.message}`)
      } finally {
        this.loadingAvatars = false
      }
    },

    async switchAvatar(avatarId) {
      if (this.switchingAvatar || !this.connected) {
        if (!this.connected) {
          alert('请先连接数字人后再切换Avatar')
        }
        return
      }

      if (this.currentAvatarId === avatarId) {
        console.log('已经是当前Avatar，无需切换')
        return
      }

      this.switchingAvatar = true
      try {
        // 尝试多个可能的API路径
        const apiUrls = ['/api/switch_avatar', '/switch_avatar']
        let response = null
        let lastError = null

        for (const url of apiUrls) {
          try {
            response = await fetch(url, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                sessionid: this.sessionId,
                avatar_id: avatarId
              })
            })

            if (response.ok) {
              break
            }
          } catch (error) {
            lastError = error
          }
        }

        if (!response || !response.ok) {
          throw lastError || new Error('切换Avatar API无法访问')
        }

        const data = await response.json()

        if (data.code === 0) {
          this.currentAvatarId = avatarId
          console.log('成功切换Avatar到:', data.avatar_id)

          // 添加系统消息
          this.messages.push({
            role: 'system',
            text: `已切换到Avatar: ${avatarId}`
          })
          this.$nextTick(() => {
            const el = this.$refs.chatBox
            if (el) el.scrollTop = el.scrollHeight
          })

          alert(`成功切换到Avatar: ${data.avatar_id}`)
        } else {
          console.error('切换Avatar失败:', data.msg)
          alert(`切换Avatar失败: ${data.msg}`)
        }
      } catch (error) {
        console.error('切换Avatar请求失败:', error)
        alert('切换Avatar失败，请检查网络连接')
      } finally {
        this.switchingAvatar = false
      }
    }
  }
}
</script>

<style scoped>
/* 主色（与原始配色兼容） */
.bg-primary { background-color: var(--primary, #4361ee); }
</style>

